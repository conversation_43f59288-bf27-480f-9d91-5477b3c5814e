import os
import shutil
import torch
from fastapi import FastAPI, UploadFile, File
from fastapi.responses import FileResponse
from tempfile import NamedTemporaryFile
import time
from huggingface_hub import snapshot_download
from models.vc.vevo.vevo_utils import VevoInferencePipeline, save_audio

app = FastAPI(title="Vevo API")

# ===== Device =====
device = torch.device("cuda") if torch.cuda.is_available() else torch.device("cpu")

# ===== Model Setup =====
tokenizer_dir = snapshot_download(
    repo_id="amphion/Vevo",
    repo_type="model",
    cache_dir="./ckpts/Vevo",
    allow_patterns=["tokenizer/vq8192/*"],
)
tokenizer_ckpt_path = os.path.join(tokenizer_dir, "tokenizer/vq8192")

fmt_dir = snapshot_download(
    repo_id="amphion/Vevo",
    repo_type="model",
    cache_dir="./ckpts/Vevo",
    allow_patterns=["acoustic_modeling/Vq8192ToMels/*"],
)
fmt_cfg_path = "./models/vc/vevo/config/Vq8192ToMels.json"
fmt_ckpt_path = os.path.join(fmt_dir, "acoustic_modeling/Vq8192ToMels")

vocoder_dir = snapshot_download(
    repo_id="amphion/Vevo",
    repo_type="model",
    cache_dir="./ckpts/Vevo",
    allow_patterns=["acoustic_modeling/Vocoder/*"],
)
vocoder_cfg_path = "./models/vc/vevo/config/Vocoder.json"
vocoder_ckpt_path = os.path.join(vocoder_dir, "acoustic_modeling/Vocoder")

# ===== Inference Pipeline Init =====
inference_pipeline = VevoInferencePipeline(
    content_style_tokenizer_ckpt_path=tokenizer_ckpt_path,
    fmt_cfg_path=fmt_cfg_path,
    fmt_ckpt_path=fmt_ckpt_path,
    vocoder_cfg_path=vocoder_cfg_path,
    vocoder_ckpt_path=vocoder_ckpt_path,
    device=device,
)


def vevo_timbre(content_wav_path, reference_wav_path, output_path):
    gen_audio = inference_pipeline.inference_fm(
        src_wav_path=content_wav_path,
        timbre_ref_wav_path=reference_wav_path,
        flow_matching_steps=32,
    )
    save_audio(gen_audio, output_path=output_path)


@app.post("/timbre-transfer/")
async def timbre_transfer(
    source_audio: UploadFile = File(...),
    reference_audio: UploadFile = File(...),
):
    with NamedTemporaryFile(delete=False, suffix=".wav") as src_temp, \
         NamedTemporaryFile(delete=False, suffix=".wav") as ref_temp, \
         NamedTemporaryFile(delete=False, suffix=".wav") as out_temp:

        shutil.copyfileobj(source_audio.file, src_temp)
        shutil.copyfileobj(reference_audio.file, ref_temp)

        src_path = src_temp.name
        ref_path = ref_temp.name
        out_path = out_temp.name

    start_time= time.time()
# Run inference
    vevo_timbre(src_path, ref_path, out_path)
    end_time = time.time()
    latency = end_time - start_time

    response = FileResponse(out_path, media_type="audio/wav", filename="output.wav")
    response.headers["X-Inference-Latency"] = str(latency)
    return response
