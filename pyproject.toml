[project]
name = "amphion"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "accelerate==0.24.1",
    "black==24.1.1",
    "cn2an>=0.5.23",
    "encodec>=0.1.1",
    "fastapi>=0.115.13",
    "g2p-en>=2.1.0",
    "gradio>=5.34.2",
    "ipykernel>=6.29.5",
    "jieba>=0.42.1",
    "json5>=0.12.0",
    "langsegment>=0.2.0",
    "librosa>=0.11.0",
    "numpy==1.26.0",
    "onnxruntime>=1.22.0",
    "openai-whisper>=20240930",
    "phonemizer>=3.3.0",
    "pykakasi>=2.3.0",
    "pyopenjtalk>=0.4.1",
    "pypinyin>=0.54.0",
    "pyworld>=0.3.5",
    "ruamel-yaml>=0.18.14",
    "scipy==1.12.0",
    "setuptools>=80.9.0",
    "spaces>=0.37.1",
    "torch==2.0.1",
    "tqdm>=4.67.1",
    "transformers==4.41.2",
    "unidecode>=1.4.0",
    "uvicorn>=0.34.3",
]
