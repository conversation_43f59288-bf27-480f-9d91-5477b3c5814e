nohup: ignoring input
/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/pyworld/__init__.py:13: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources

Fetching 1 files:   0%|          | 0/1 [00:00<?, ?it/s]
Fetching 1 files: 100%|██████████| 1/1 [00:00<00:00, 18808.54it/s]

Fetching 1 files:   0%|          | 0/1 [00:00<?, ?it/s]
Fetching 1 files: 100%|██████████| 1/1 [00:00<00:00, 2816.86it/s]

Fetching 3 files:   0%|          | 0/3 [00:00<?, ?it/s]
Fetching 3 files: 100%|██████████| 3/3 [00:00<00:00, 6543.38it/s]
INFO:     Started server process [517835]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8787 (Press CTRL+C to quit)
#Params of Flow Matching model: 337.69 M
#Params of Vocoder model: 255.04 M
#Params of Content-Style Tokenizer: 44.29 M
INFO:     172.16.20.8:62298 - "GET /docs HTTP/1.1" 200 OK
INFO:     172.16.20.8:62298 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     172.16.20.8:62320 - "POST /timbre-transfer/ HTTP/1.1" 500 Internal Server Error
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/uvicorn/protocols/http/h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 187, in __call__
    raise exc
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/fastapi_server.py", line 81, in timbre_transfer
    vevo_timbre(src_path, ref_path, out_path)
  File "/root/ai_compute/Amphion/fastapi_server.py", line 55, in vevo_timbre
    gen_audio = inference_pipeline.inference_fm(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/models/vc/vevo/vevo_utils.py", line 608, in inference_fm
    predict_mel_feat = self.fmt_model.reverse_diffusion(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/torch/utils/_contextlib.py", line 115, in decorate_context
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/models/vc/flow_matching_transformer/fmt_model.py", line 263, in reverse_diffusion
    flow_pred = self.diff_estimator(xt_input, t, cond, xt_mask)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/torch/nn/modules/module.py", line 1501, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/models/vc/flow_matching_transformer/llama_nar.py", line 354, in forward
    layer_outputs = decoder_layer(
                    ^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/torch/nn/modules/module.py", line 1501, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/models/vc/flow_matching_transformer/llama_nar.py", line 98, in forward
    hidden_states, self_attn_weights, present_key_value = self.self_attn(
                                                          ^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/torch/nn/modules/module.py", line 1501, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/transformers/models/llama/modeling_llama.py", line 346, in forward
    attn_weights = torch.matmul(query_states, key_states.transpose(2, 3)) / math.sqrt(self.head_dim)
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~~~~~~~~~
torch.cuda.OutOfMemoryError: CUDA out of memory. Tried to allocate 3.36 GiB (GPU 0; 14.58 GiB total capacity; 7.40 GiB already allocated; 1.22 GiB free; 8.08 GiB reserved in total by PyTorch) If reserved memory is >> allocated memory try setting max_split_size_mb to avoid fragmentation.  See documentation for Memory Management and PYTORCH_CUDA_ALLOC_CONF
INFO:     172.16.20.8:62329 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.8:62375 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.8:62443 - "POST /timbre-transfer/ HTTP/1.1" 500 Internal Server Error
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/uvicorn/protocols/http/h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 187, in __call__
    raise exc
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/fastapi_server.py", line 81, in timbre_transfer
    vevo_timbre(src_path, ref_path, out_path)
  File "/root/ai_compute/Amphion/fastapi_server.py", line 55, in vevo_timbre
    gen_audio = inference_pipeline.inference_fm(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/models/vc/vevo/vevo_utils.py", line 597, in inference_fm
    src_hubert_codecs, _ = self.extract_hubert_codec(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/torch/utils/_contextlib.py", line 115, in decorate_context
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/models/vc/vevo/vevo_utils.py", line 341, in extract_hubert_codec
    feats, feat_lengths = self.extract_hubert_feature(wavs, wav_lens, output_layer)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/torch/utils/_contextlib.py", line 115, in decorate_context
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/models/vc/vevo/vevo_utils.py", line 300, in extract_hubert_feature
    feats, feat_lengths = self.hubert_model.extract_features(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/torchaudio/pipelines/_wav2vec2/impl.py", line 38, in extract_features
    return self.model.extract_features(waveforms, lengths, num_layers)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/torchaudio/models/wav2vec2/model.py", line 84, in extract_features
    x = self.encoder.extract_features(x, lengths, num_layers)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/torchaudio/models/wav2vec2/components.py", line 514, in extract_features
    return self.transformer.get_intermediate_outputs(x, attention_mask=masks, num_layers=num_layers)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/torchaudio/models/wav2vec2/components.py", line 463, in get_intermediate_outputs
    x, _ = layer(x, attention_mask)  # Ignore position_bias
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/torch/nn/modules/module.py", line 1501, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/torchaudio/models/wav2vec2/components.py", line 394, in forward
    x, position_bias = self.attention(
                       ^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/torch/nn/modules/module.py", line 1501, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/torchaudio/models/wav2vec2/components.py", line 310, in forward
    attn_output = torch.nn.functional.scaled_dot_product_attention(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
torch.cuda.OutOfMemoryError: CUDA out of memory. Tried to allocate 2.06 GiB (GPU 0; 14.58 GiB total capacity; 5.86 GiB already allocated; 1.75 GiB free; 7.55 GiB reserved in total by PyTorch) If reserved memory is >> allocated memory try setting max_split_size_mb to avoid fragmentation.  See documentation for Memory Management and PYTORCH_CUDA_ALLOC_CONF
INFO:     172.16.20.8:62503 - "POST /timbre-transfer/ HTTP/1.1" 500 Internal Server Error
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/uvicorn/protocols/http/h11_impl.py", line 403, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 187, in __call__
    raise exc
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/routing.py", line 714, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/routing.py", line 734, in app
    await route.handle(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/fastapi_server.py", line 81, in timbre_transfer
    vevo_timbre(src_path, ref_path, out_path)
  File "/root/ai_compute/Amphion/fastapi_server.py", line 55, in vevo_timbre
    gen_audio = inference_pipeline.inference_fm(
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/models/vc/vevo/vevo_utils.py", line 617, in inference_fm
    self.vocoder_model(predict_mel_feat.transpose(1, 2)).detach().cpu()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/torch/nn/modules/module.py", line 1501, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/models/codec/amphion_codec/vocos.py", line 878, in forward
    x = self.backbone(x)
        ^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/torch/nn/modules/module.py", line 1501, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/models/codec/amphion_codec/vocos.py", line 781, in forward
    x = conv_block(x, cond_embedding_id=bandwidth_id)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/torch/nn/modules/module.py", line 1501, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/models/codec/amphion_codec/vocos.py", line 520, in forward
    x = self.act(x)
        ^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/torch/nn/modules/module.py", line 1501, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/torch/nn/modules/activation.py", line 685, in forward
    return F.gelu(input, approximate=self.approximate)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
torch.cuda.OutOfMemoryError: CUDA out of memory. Tried to allocate 72.00 MiB (GPU 0; 14.58 GiB total capacity; 9.19 GiB already allocated; 47.62 MiB free; 9.26 GiB reserved in total by PyTorch) If reserved memory is >> allocated memory try setting max_split_size_mb to avoid fragmentation.  See documentation for Memory Management and PYTORCH_CUDA_ALLOC_CONF
INFO:     172.16.20.8:62523 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.8:62568 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.8:62640 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     10.130.233.194:51085 - "GET / HTTP/1.1" 404 Not Found
INFO:     10.130.233.194:51085 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     10.130.233.194:51086 - "GET /docs HTTP/1.1" 200 OK
INFO:     10.130.233.194:51086 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     10.130.233.194:51087 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.8:62944 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.8:62974 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.8:62997 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.8:63028 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.35:52458 - "GET /docs HTTP/1.1" 200 OK
INFO:     172.16.20.35:52458 - "GET /openapi.json HTTP/1.1" 200 OK
/root/ai_compute/Amphion/models/vc/vevo/vevo_utils.py:141: UserWarning: PySoundFile failed. Trying audioread instead.
  speech = librosa.load(wav_path, sr=24000)[0]
/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/librosa/core/audio.py:184: FutureWarning: librosa.core.audio.__audioread_load
	Deprecated as of librosa version 0.10.0.
	It will be removed in librosa version 1.0.
  y, sr_native = __audioread_load(path, offset, duration, dtype)
INFO:     172.16.20.35:52475 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
/root/ai_compute/Amphion/models/vc/vevo/vevo_utils.py:141: UserWarning: PySoundFile failed. Trying audioread instead.
  speech = librosa.load(wav_path, sr=24000)[0]
/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/librosa/core/audio.py:184: FutureWarning: librosa.core.audio.__audioread_load
	Deprecated as of librosa version 0.10.0.
	It will be removed in librosa version 1.0.
  y, sr_native = __audioread_load(path, offset, duration, dtype)
INFO:     172.16.20.35:52491 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.35:52972 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.35:53011 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.8:63435 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.8:63706 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     10.130.233.194:52866 - "GET /docs HTTP/1.1" 200 OK
INFO:     10.130.233.194:53193 - "GET /docs HTTP/1.1" 200 OK
INFO:     10.130.233.194:53193 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     10.130.233.194:53194 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.35:54440 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     10.130.233.194:53222 - "GET /docs HTTP/1.1" 200 OK
INFO:     10.130.233.194:53222 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     10.130.233.194:53223 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     10.130.233.194:53301 - "GET /docs HTTP/1.1" 200 OK
INFO:     10.130.233.194:53301 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     172.16.20.35:54641 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.35:54663 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     10.130.233.194:53400 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     10.130.233.194:53418 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     10.130.233.194:53425 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.8:64154 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.35:56501 - "GET /docs HTTP/1.1" 200 OK
INFO:     172.16.20.73:58016 - "GET /docs HTTP/1.1" 200 OK
INFO:     172.16.20.73:58016 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     172.16.20.73:58034 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.73:58035 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.73:58043 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
/root/ai_compute/Amphion/models/vc/vevo/vevo_utils.py:141: UserWarning: PySoundFile failed. Trying audioread instead.
  speech = librosa.load(wav_path, sr=24000)[0]
/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/librosa/core/audio.py:184: FutureWarning: librosa.core.audio.__audioread_load
	Deprecated as of librosa version 0.10.0.
	It will be removed in librosa version 1.0.
  y, sr_native = __audioread_load(path, offset, duration, dtype)
INFO:     172.16.20.73:58050 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
/root/ai_compute/Amphion/models/vc/vevo/vevo_utils.py:141: UserWarning: PySoundFile failed. Trying audioread instead.
  speech = librosa.load(wav_path, sr=24000)[0]
/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/librosa/core/audio.py:184: FutureWarning: librosa.core.audio.__audioread_load
	Deprecated as of librosa version 0.10.0.
	It will be removed in librosa version 1.0.
  y, sr_native = __audioread_load(path, offset, duration, dtype)
INFO:     172.16.20.73:58051 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
/root/ai_compute/Amphion/models/vc/vevo/vevo_utils.py:141: UserWarning: PySoundFile failed. Trying audioread instead.
  speech = librosa.load(wav_path, sr=24000)[0]
/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/librosa/core/audio.py:184: FutureWarning: librosa.core.audio.__audioread_load
	Deprecated as of librosa version 0.10.0.
	It will be removed in librosa version 1.0.
  y, sr_native = __audioread_load(path, offset, duration, dtype)
INFO:     172.16.20.73:58123 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
/root/ai_compute/Amphion/models/vc/vevo/vevo_utils.py:141: UserWarning: PySoundFile failed. Trying audioread instead.
  speech = librosa.load(wav_path, sr=24000)[0]
/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/librosa/core/audio.py:184: FutureWarning: librosa.core.audio.__audioread_load
	Deprecated as of librosa version 0.10.0.
	It will be removed in librosa version 1.0.
  y, sr_native = __audioread_load(path, offset, duration, dtype)
INFO:     172.16.20.73:58143 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
/root/ai_compute/Amphion/models/vc/vevo/vevo_utils.py:141: UserWarning: PySoundFile failed. Trying audioread instead.
  speech = librosa.load(wav_path, sr=24000)[0]
/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/librosa/core/audio.py:184: FutureWarning: librosa.core.audio.__audioread_load
	Deprecated as of librosa version 0.10.0.
	It will be removed in librosa version 1.0.
  y, sr_native = __audioread_load(path, offset, duration, dtype)
INFO:     172.16.20.73:58144 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
/root/ai_compute/Amphion/models/vc/vevo/vevo_utils.py:141: UserWarning: PySoundFile failed. Trying audioread instead.
  speech = librosa.load(wav_path, sr=24000)[0]
/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/librosa/core/audio.py:184: FutureWarning: librosa.core.audio.__audioread_load
	Deprecated as of librosa version 0.10.0.
	It will be removed in librosa version 1.0.
  y, sr_native = __audioread_load(path, offset, duration, dtype)
INFO:     172.16.20.73:58149 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.73:58153 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
/root/ai_compute/Amphion/models/vc/vevo/vevo_utils.py:141: UserWarning: PySoundFile failed. Trying audioread instead.
  speech = librosa.load(wav_path, sr=24000)[0]
/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/librosa/core/audio.py:184: FutureWarning: librosa.core.audio.__audioread_load
	Deprecated as of librosa version 0.10.0.
	It will be removed in librosa version 1.0.
  y, sr_native = __audioread_load(path, offset, duration, dtype)
INFO:     172.16.20.73:58159 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.8:64964 - "GET /docs HTTP/1.1" 200 OK
INFO:     172.16.20.8:64964 - "GET /openapi.json HTTP/1.1" 200 OK
/root/ai_compute/Amphion/models/vc/vevo/vevo_utils.py:141: UserWarning: PySoundFile failed. Trying audioread instead.
  speech = librosa.load(wav_path, sr=24000)[0]
/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/librosa/core/audio.py:184: FutureWarning: librosa.core.audio.__audioread_load
	Deprecated as of librosa version 0.10.0.
	It will be removed in librosa version 1.0.
  y, sr_native = __audioread_load(path, offset, duration, dtype)
INFO:     172.16.20.73:58187 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
/root/ai_compute/Amphion/models/vc/vevo/vevo_utils.py:141: UserWarning: PySoundFile failed. Trying audioread instead.
  speech = librosa.load(wav_path, sr=24000)[0]
/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/librosa/core/audio.py:184: FutureWarning: librosa.core.audio.__audioread_load
	Deprecated as of librosa version 0.10.0.
	It will be removed in librosa version 1.0.
  y, sr_native = __audioread_load(path, offset, duration, dtype)
INFO:     172.16.20.73:58195 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
/root/ai_compute/Amphion/models/vc/vevo/vevo_utils.py:141: UserWarning: PySoundFile failed. Trying audioread instead.
  speech = librosa.load(wav_path, sr=24000)[0]
/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/librosa/core/audio.py:184: FutureWarning: librosa.core.audio.__audioread_load
	Deprecated as of librosa version 0.10.0.
	It will be removed in librosa version 1.0.
  y, sr_native = __audioread_load(path, offset, duration, dtype)
INFO:     172.16.20.73:58206 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.8:65101 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
/root/ai_compute/Amphion/models/vc/vevo/vevo_utils.py:141: UserWarning: PySoundFile failed. Trying audioread instead.
  speech = librosa.load(wav_path, sr=24000)[0]
/root/ai_compute/Amphion/.venv/lib/python3.11/site-packages/librosa/core/audio.py:184: FutureWarning: librosa.core.audio.__audioread_load
	Deprecated as of librosa version 0.10.0.
	It will be removed in librosa version 1.0.
  y, sr_native = __audioread_load(path, offset, duration, dtype)
INFO:     172.16.20.73:58375 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.73:58469 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.73:58470 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.73:58475 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.73:58497 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.73:58499 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.73:58508 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.73:58511 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     172.16.20.73:58514 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     10.130.233.3:58566 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     10.130.233.3:58567 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
INFO:     10.130.233.194:55888 - "GET /docs HTTP/1.1" 200 OK
INFO:     172.16.20.36:58807 - "GET /docs HTTP/1.1" 200 OK
INFO:     172.16.20.36:58807 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     172.16.20.36:58808 - "GET /docs HTTP/1.1" 200 OK
INFO:     172.16.20.36:58808 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     172.16.20.30:53548 - "GET /docs HTTP/1.1" 200 OK
INFO:     172.16.20.30:53548 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     172.16.20.30:53558 - "POST /timbre-transfer/ HTTP/1.1" 200 OK
